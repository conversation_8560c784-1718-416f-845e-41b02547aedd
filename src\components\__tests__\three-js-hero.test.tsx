/**
 * @jest-environment jsdom
 */

import React from 'react';
import { render, screen } from '@testing-library/react';
import { ThemeProvider } from 'next-themes';
import ThreeJsHero from '../three-js-hero';
import { defaultAnimationConfig } from '@/constants/animations';

// Mock react-i18next
jest.mock('react-i18next', () => ({
  useTranslation: () => ({
    t: (key: string) => key,
    i18n: {
      language: 'en',
    },
  }),
}));

// Mock @react-three/fiber
jest.mock('@react-three/fiber', () => ({
  Canvas: ({ children, ...props }: React.PropsWithChildren<unknown>) => (
    <div data-testid="three-canvas" {...props}>
      {children}
    </div>
  ),
  useFrame: jest.fn(),
  useThree: () => ({
    scene: {},
    camera: {},
    gl: {},
  }),
}));

// <PERSON><PERSON> @react-three/drei
jest.mock('@react-three/drei', () => ({
  OrbitControls: () => <div data-testid="orbit-controls" />,
  Environment: () => <div data-testid="environment" />,
  Html: ({ children }: React.PropsWithChildren) => (
    <div data-testid="html-overlay">{children}</div>
  ),
  useProgress: () => ({ progress: 50 }),
  Stats: () => <div data-testid="stats" />,
  PerspectiveCamera: () => <div data-testid="camera" />,
}));

// Mock motion
jest.mock('motion/react', () => ({
  motion: {
    div: ({ children, ...props }: React.PropsWithChildren<unknown>) => (
      <div {...props}>{children}</div>
    ),
  },
}));

// Mock next-themes
jest.mock('next-themes', () => ({
  useTheme: () => ({
    theme: 'light',
    setTheme: jest.fn(),
  }),
  ThemeProvider: ({ children }: React.PropsWithChildren) => children,
}));

const defaultProps = {
  title: 'Test Hero Title',
  description: 'Test hero description',
  buttonText: 'Test Button',
  buttonHref: '/test',
  animationConfig: defaultAnimationConfig,
};

const renderWithTheme = (component: React.ReactElement) => {
  return render(
    <ThemeProvider attribute="class" defaultTheme="light">
      {component}
    </ThemeProvider>
  );
};

describe('ThreeJsHero Component', () => {
  beforeEach(() => {
    // Clear all mocks before each test
    jest.clearAllMocks();
  });

  it('renders without crashing', () => {
    renderWithTheme(<ThreeJsHero {...defaultProps} />);
    expect(screen.getByText('Test Hero Title')).toBeInTheDocument();
  });

  it('displays the correct title and description', () => {
    renderWithTheme(<ThreeJsHero {...defaultProps} />);

    expect(screen.getByText('Test Hero Title')).toBeInTheDocument();
    expect(screen.getByText('Test hero description')).toBeInTheDocument();
  });

  it('renders the CTA button with correct text and href', () => {
    renderWithTheme(<ThreeJsHero {...defaultProps} />);

    const button = screen.getByRole('link');
    expect(button).toHaveTextContent('Test Button');
    expect(button).toHaveAttribute('href', '/test');
  });

  it('renders the Three.js canvas', () => {
    renderWithTheme(<ThreeJsHero {...defaultProps} />);

    expect(screen.getByTestId('three-canvas')).toBeInTheDocument();
  });

  it('applies custom className', () => {
    const customClass = 'custom-hero-class';
    renderWithTheme(<ThreeJsHero {...defaultProps} className={customClass} />);

    const heroSection = screen.getByRole('region', { hidden: true });
    expect(heroSection).toHaveClass(customClass);
  });

  it('shows controls info when enableControls is true', () => {
    renderWithTheme(<ThreeJsHero {...defaultProps} enableControls={true} />);

    expect(screen.getByText(/Click and drag to rotate/)).toBeInTheDocument();
  });

  it('does not show controls info when enableControls is false', () => {
    renderWithTheme(<ThreeJsHero {...defaultProps} enableControls={false} />);

    expect(
      screen.queryByText(/Click and drag to rotate/)
    ).not.toBeInTheDocument();
  });

  it('handles HTML in title correctly', () => {
    const htmlTitle = 'Welcome to <span>Centris</span>';
    renderWithTheme(<ThreeJsHero {...defaultProps} title={htmlTitle} />);

    const titleElement = screen.getByRole('heading', { level: 1 });
    expect(titleElement.innerHTML).toContain('<span>Centris</span>');
  });

  it('applies correct styling classes', () => {
    renderWithTheme(<ThreeJsHero {...defaultProps} />);

    const heroSection = screen.getByRole('region', { hidden: true });
    expect(heroSection).toHaveClass(
      'relative',
      'h-screen',
      'w-full',
      'overflow-hidden'
    );
  });

  it('renders with minimal animation config', () => {
    const minimalConfig = {
      scene: { background: '#000000' },
      camera: { position: [0, 0, 5] as [number, number, number] },
      lights: [],
      objects: [],
      performance: { antialias: false },
    };

    renderWithTheme(
      <ThreeJsHero {...defaultProps} animationConfig={minimalConfig} />
    );

    expect(screen.getByTestId('three-canvas')).toBeInTheDocument();
  });

  it('handles missing optional props gracefully', () => {
    const minimalProps = {
      title: 'Test',
      description: 'Test description',
      buttonText: 'Button',
      buttonHref: '/test',
      animationConfig: defaultAnimationConfig,
    };

    renderWithTheme(<ThreeJsHero {...minimalProps} />);

    expect(screen.getByText('Test')).toBeInTheDocument();
  });
});

describe('ThreeJsHero Accessibility', () => {
  it('has proper heading structure', () => {
    renderWithTheme(<ThreeJsHero {...defaultProps} />);

    const heading = screen.getByRole('heading', { level: 1 });
    expect(heading).toBeInTheDocument();
    expect(heading).toHaveTextContent('Test Hero Title');
  });

  it('has accessible button', () => {
    renderWithTheme(<ThreeJsHero {...defaultProps} />);

    const button = screen.getByRole('link');
    expect(button).toBeInTheDocument();
    expect(button).toHaveAttribute('href', '/test');
  });

  it('provides meaningful text content', () => {
    renderWithTheme(<ThreeJsHero {...defaultProps} />);

    expect(screen.getByText('Test Hero Title')).toBeInTheDocument();
    expect(screen.getByText('Test hero description')).toBeInTheDocument();
    expect(screen.getByText('Test Button')).toBeInTheDocument();
  });
});

describe('ThreeJsHero Performance', () => {
  it('applies performance settings from config', () => {
    const performanceConfig = {
      ...defaultAnimationConfig,
      performance: {
        antialias: false,
        shadowMapEnabled: false,
        pixelRatio: 1,
      },
    };

    renderWithTheme(
      <ThreeJsHero {...defaultProps} animationConfig={performanceConfig} />
    );

    expect(screen.getByTestId('three-canvas')).toBeInTheDocument();
  });

  it('handles showStats prop correctly', () => {
    // Mock NODE_ENV for this test
    const originalEnv = process.env.NODE_ENV;
    process.env.NODE_ENV = 'development';

    renderWithTheme(<ThreeJsHero {...defaultProps} showStats={true} />);

    // In development mode with showStats=true, Stats should be rendered
    // Note: This test might need adjustment based on actual implementation

    // Restore original NODE_ENV
    process.env.NODE_ENV = originalEnv;
  });
});
