'use client';

import React from 'react';
import { useTranslation } from 'react-i18next';
import ThreeJsHero from '@/components/three-js-hero';
import {
  defaultAnimationConfig,
  aboutAnimationConfig,
} from '@/constants/animations';

/**
 * Example usage of ThreeJsHero component with i18next internationalization
 *
 * This component demonstrates:
 * - Multilingual support using react-i18next
 * - Different animation configurations for different contexts
 * - Proper integration with the project's design system
 * - Responsive design and accessibility considerations
 */

// Homepage Hero Example
export const HomepageHeroExample = () => {
  const { t } = useTranslation('common');

  return (
    <ThreeJsHero
      title={t('hero.title')}
      description={t('hero.description')}
      buttonText={t('navigation.button.contact')}
      buttonHref="/contact"
      animationConfig={defaultAnimationConfig}
      enableControls={false}
      autoRotate={false}
      showStats={false}
      className="min-h-screen"
    />
  );
};

// About Page Hero Example
export const AboutHeroExample = () => {
  const { t } = useTranslation('about');
  const { t: commonT } = useTranslation('common');

  return (
    <ThreeJsHero
      title={t('hero.title', 'About Centris')}
      description={t(
        'hero.description',
        'Learn more about our innovative document processing solutions and how we transform businesses worldwide.'
      )}
      buttonText={commonT('navigation.button.contact')}
      buttonHref="/contact"
      animationConfig={aboutAnimationConfig}
      enableControls={true}
      autoRotate={true}
      showStats={false}
      className="h-[80vh]"
    />
  );
};

// Products Page Hero Example
export const ProductsHeroExample = () => {
  const { t } = useTranslation('products');

  // Custom animation config for products page
  const productsAnimationConfig = {
    ...defaultAnimationConfig,
    camera: {
      position: [0, 3, 10] as [number, number, number],
      fov: 65,
    },
    objects: [
      // Simplified version with fewer objects for better performance
      ...defaultAnimationConfig.objects.slice(0, 4),
    ],
    particles: [
      // Reduced particle count for products page
      {
        ...defaultAnimationConfig.particles![0],
        count: 100,
      },
    ],
  };

  return (
    <ThreeJsHero
      title={t('header.title')}
      description={t('header.description')}
      buttonText={t('buttons.explore')}
      buttonHref="/products"
      animationConfig={productsAnimationConfig}
      enableControls={false}
      autoRotate={false}
      showStats={false}
      className="h-screen"
    />
  );
};

// Interactive Demo Example (with controls enabled)
export const InteractiveDemoExample = () => {
  const { t } = useTranslation('common');

  // Enhanced animation config for interactive demo
  const demoAnimationConfig = {
    ...defaultAnimationConfig,
    performance: {
      ...defaultAnimationConfig.performance,
      shadowMapEnabled: true,
      antialias: true,
    },
  };

  return (
    <div className="relative">
      <ThreeJsHero
        title="Interactive 3D Demo"
        description={t('site.description')}
        buttonText="Explore Features"
        buttonHref="/products"
        animationConfig={demoAnimationConfig}
        enableControls={true}
        autoRotate={true}
        showStats={true}
        className="h-screen"
      />

      {/* Additional UI overlay for demo */}
      <div className="bg-card/90 absolute top-4 right-4 z-20 max-w-sm rounded-lg p-4 backdrop-blur-sm">
        <h3 className="text-card-foreground mb-2 font-semibold">
          Interactive Demo
        </h3>
        <ul className="text-muted-foreground space-y-1 text-sm">
          <li>• Drag to rotate the scene</li>
          <li>• Scroll to zoom in/out</li>
          <li>• Watch the animated elements</li>
          <li>• Experience real-time 3D graphics</li>
        </ul>
      </div>
    </div>
  );
};

// Minimal Hero Example (for performance-critical pages)
export const MinimalHeroExample = () => {
  const { t } = useTranslation('common');

  // Minimal animation config for better performance
  const minimalAnimationConfig = {
    scene: {
      background: 'hsl(var(--background))',
    },
    camera: {
      position: [0, 0, 5] as [number, number, number],
      fov: 75,
    },
    lights: [
      {
        type: 'ambient' as const,
        color: 'hsl(var(--foreground))',
        intensity: 0.6,
      },
      {
        type: 'directional' as const,
        color: 'hsl(var(--primary))',
        intensity: 1,
        position: [2, 2, 2] as [number, number, number],
      },
    ],
    objects: [
      {
        type: 'sphere' as const,
        position: [0, 0, 0] as [number, number, number],
        material: {
          color: 'hsl(var(--primary))',
          metalness: 0.7,
          roughness: 0.3,
        },
        animation: {
          type: 'rotate' as const,
          speed: 0.5,
          axis: 'y' as const,
        },
      },
    ],
    performance: {
      antialias: false,
      shadowMapEnabled: false,
      pixelRatio: 1,
    },
  };

  return (
    <ThreeJsHero
      title={t('hero.title')}
      description={t('hero.description')}
      buttonText={t('navigation.button.contact')}
      buttonHref="/contact"
      animationConfig={minimalAnimationConfig}
      enableControls={false}
      autoRotate={false}
      showStats={false}
      className="h-[60vh]"
    />
  );
};

// Export all examples for easy importing
export const ThreeJsHeroExamples = {
  Homepage: HomepageHeroExample,
  About: AboutHeroExample,
  Products: ProductsHeroExample,
  InteractiveDemo: InteractiveDemoExample,
  Minimal: MinimalHeroExample,
};
