'use client';

import React, { useEffect, useState } from 'react';
import { useTranslation, Trans } from 'react-i18next';
import { motion } from 'motion/react';
import { FileText, Zap, Shield, ArrowRight, Play, FileCheck, Search, Archive } from 'lucide-react';
import GradientHighlighter from '@/components/gradient-highlighter';
import FloatingDocumentCard from '@/components/floating-doc-card';
import {
  CARD_POSITIONS_MOBILE,
  CARD_POSITIONS_TABLET,
  CARD_POSITIONS_DESKTOP,
} from '@/constants/hero';

const Hero = () => {
  const { t } = useTranslation('products');

  const [dimensions, setDimensions] = useState({ width: 0, height: 0 });

  useEffect(() => {
    const updateDimensions = () => {
      setDimensions({ width: window.innerWidth, height: window.innerHeight });
    };

    updateDimensions();
    window.addEventListener('resize', updateDimensions);
    return () => window.removeEventListener('resize', updateDimensions);
  }, []);

  const isMobile = dimensions.width < 768;
  const isTablet = dimensions.width >= 768 && dimensions.width < 1024;

  const getCardPositions = () => {
    if (isMobile) {
      return CARD_POSITIONS_MOBILE;
    } else if (isTablet) {
      return CARD_POSITIONS_TABLET;
    } else {
      return CARD_POSITIONS_DESKTOP;
    }
  };

  const positions = getCardPositions();

  return (
    <section className="relative flex min-h-screen items-center justify-center overflow-hidden bg-gradient-to-br from-background via-muted/20 to-background">
      {/* Background decorative elements */}
      <div className="pointer-events-none absolute inset-0">
        {/* Large gradient background orbs */}
        <motion.div
          initial={{ opacity: 0, scale: 0.8 }}
          animate={{ opacity: 0.3, scale: 1 }}
          transition={{ duration: 2 }}
          className="absolute -top-1/4 -left-1/4 h-1/2 w-1/2 rounded-full bg-gradient-to-br from-blue-500/40 to-cyan-500/30 blur-3xl dark:from-blue-500/20 dark:to-cyan-500/15"
        />
        <motion.div
          initial={{ opacity: 0, scale: 0.8 }}
          animate={{ opacity: 0.25, scale: 1 }}
          transition={{ duration: 2, delay: 0.5 }}
          className="absolute -right-1/4 -bottom-1/4 h-1/2 w-1/2 rounded-full bg-gradient-to-br from-indigo-500/30 to-purple-500/25 blur-3xl dark:from-indigo-500/15 dark:to-purple-500/10"
        />

        {/* Document processing themed floating icons */}
        <motion.div
          animate={{
            y: [0, -20, 0],
            rotate: [0, 5, 0],
          }}
          transition={{
            duration: 6,
            repeat: Infinity,
            ease: 'easeInOut',
          }}
          className="absolute top-20 left-[8%] h-16 w-16 rounded-xl bg-gradient-to-br from-blue-500/20 to-cyan-500/20 p-3 backdrop-blur-sm border border-blue-400/30"
        >
          <FileText className="h-full w-full text-blue-600 dark:text-blue-400" />
        </motion.div>

        <motion.div
          animate={{
            y: [0, 15, 0],
            rotate: [0, -3, 0],
          }}
          transition={{
            duration: 8,
            repeat: Infinity,
            ease: 'easeInOut',
            delay: 1,
          }}
          className="absolute top-32 right-[10%] h-12 w-12 rounded-lg bg-gradient-to-br from-cyan-400/20 to-blue-500/20 p-2 backdrop-blur-sm border border-cyan-400/30"
        >
          <Search className="h-full w-full text-cyan-600 dark:text-cyan-400" />
        </motion.div>

        <motion.div
          animate={{
            y: [0, -10, 0],
            rotate: [0, 2, 0],
          }}
          transition={{
            duration: 7,
            repeat: Infinity,
            ease: 'easeInOut',
            delay: 2,
          }}
          className="absolute bottom-32 left-[12%] h-14 w-14 rounded-xl bg-gradient-to-br from-sky-500/20 to-blue-600/20 p-3 backdrop-blur-sm border border-sky-400/30"
        >
          <Archive className="h-full w-full text-sky-600 dark:text-sky-400" />
        </motion.div>

        <motion.div
          animate={{
            y: [0, 12, 0],
            rotate: [0, -2, 0],
          }}
          transition={{
            duration: 9,
            repeat: Infinity,
            ease: 'easeInOut',
            delay: 3,
          }}
          className="absolute bottom-20 right-[8%] h-10 w-10 rounded-lg bg-gradient-to-br from-indigo-500/20 to-purple-500/20 p-2 backdrop-blur-sm border border-indigo-400/30"
        >
          <FileCheck className="h-full w-full text-indigo-600 dark:text-indigo-400" />
        </motion.div>

        {/* Enhanced grid overlay */}
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 0.15 }}
          transition={{ duration: 2, delay: 1 }}
          className="absolute inset-0 dark:opacity-8"
          style={{
            backgroundImage:
              'linear-gradient(rgba(59, 130, 246, 0.15) 1px, transparent 1px), linear-gradient(90deg, rgba(59, 130, 246, 0.15) 1px, transparent 1px)',
            backgroundSize: '60px 60px',
          }}
        />
      </div>

      {/* Overlay with better opacity */}
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 1.5, delay: 0.3 }}
        className="absolute inset-0 z-10 bg-white/30 backdrop-blur-sm dark:bg-black/40 md:backdrop-blur-xxs"
      />

      {/* Main content - Centered */}
      <div className="relative z-20 w-full px-4">
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.5 }}
          className="mx-auto max-w-4xl text-center"
        >
          {/* Main heading with gradient highlights */}
          <Trans
            i18nKey="header.title"
            ns="products"
            parent={motion.h1}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.7 }}
            className="mb-6 text-4xl leading-tight font-bold text-gray-900 text-shadow-md sm:text-5xl md:text-6xl lg:text-7xl xl:text-6xl dark:text-white"
            components={{
              highlighted1: <GradientHighlighter />,
              highlighted2: <GradientHighlighter />,
              br: <br />,
            }}
          />

          {/* Description */}
          <motion.p
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.9 }}
            className="mx-auto mb-12 max-w-2xl text-lg leading-relaxed font-medium text-gray-700 text-shadow-xs sm:text-xl md:text-2xl dark:text-gray-300"
          >
            {t('header.description')}
          </motion.p>

          {/* Action Buttons - Centris Design */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 1.1 }}
            className="flex flex-col items-center gap-4 sm:flex-row sm:justify-center"
          >
            <button className="group transform rounded-xl bg-gradient-to-r from-blue-600 to-cyan-600 px-8 py-4 font-semibold text-white shadow-lg transition-all duration-300 hover:scale-105 hover:from-blue-700 hover:to-cyan-700 hover:shadow-xl">
              {t('buttons.explore')}
              <ArrowRight className="ml-2 h-5 w-5 transition-transform group-hover:translate-x-1" />
            </button>

            <button className="group rounded-xl border-2 border-gray-300 bg-white/80 px-8 py-4 font-semibold text-gray-700 shadow-lg backdrop-blur-sm transition-all duration-300 hover:border-blue-500 hover:bg-blue-50 hover:text-blue-700 hover:shadow-xl dark:border-gray-600 dark:bg-gray-800/80 dark:text-gray-200 dark:hover:border-blue-400 dark:hover:bg-gray-700/80 dark:hover:text-blue-300">
              <Play className="mr-2 h-5 w-5 transition-transform group-hover:scale-110" />
              {t('buttons.demo')}
            </button>
          </motion.div>

          {/* Additional info or stats */}
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.8, delay: 1.3 }}
            className="mt-12 flex flex-wrap justify-center gap-8 text-sm text-gray-600 dark:text-gray-400"
          >
            <div className="flex items-center gap-2">
              <div className="h-2 w-2 rounded-full bg-green-500"></div>
              <span>99.9% Uptime</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="h-2 w-2 rounded-full bg-blue-500"></div>
              <span>Enterprise Security</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="h-2 w-2 rounded-full bg-purple-500"></div>
              <span>24/7 Support</span>
            </div>
          </motion.div>
        </div>
      </div>

      {/* Floating Document Cards with better spacing */}
      <FloatingDocumentCard
        position={positions.card1}
        delay={0}
        title="Invoice Processing"
        type="PDF"
      />

      <FloatingDocumentCard
        position={positions.card2}
        delay={1}
        title="Contract Management"
        type="DOCX"
      />

      <FloatingDocumentCard
        position={positions.card3}
        delay={2}
        title="Digital Archive"
        type="XLSX"
      />

      {/* Fourth card for larger screens */}
      {positions.card4 && (
        <FloatingDocumentCard
          position={positions.card4}
          delay={2.5}
          title="Data Mining"
          type="CSV"
        />
      )}
    </section>
  );
};

export default Hero;
