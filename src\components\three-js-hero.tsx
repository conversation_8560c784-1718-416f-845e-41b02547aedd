'use client';

import React, { useRef, useMemo, Suspense } from 'react';
import { Canvas, useFrame } from '@react-three/fiber';
import {
  OrbitControls,
  Environment,
  Html,
  useProgress,
  Stats,
  PerspectiveCamera,
} from '@react-three/drei';
import { motion } from 'motion/react';
import { useTheme } from 'next-themes';
import * as THREE from 'three';

import { Button } from '@/components/ui/button';
import { AnimationConfig, ThreeJsHeroProps } from '@/types';

// Loading component for 3D scene
const Loader = () => {
  const { progress } = useProgress();

  return (
    <Html center>
      <div className="flex flex-col items-center justify-center">
        <div className="border-primary mb-4 h-16 w-16 animate-spin rounded-full border-4 border-t-transparent" />
        <p className="text-foreground text-sm">
          Loading 3D Scene... {Math.round(progress)}%
        </p>
      </div>
    </Html>
  );
};

// Animated object component with advanced animations
const AnimatedObject = ({
  config,
}: {
  config: AnimationConfig['objects'][0];
  index: number;
}) => {
  const meshRef = useRef<THREE.Mesh>(null);

  useFrame((state) => {
    if (!meshRef.current || !config.animation) return;

    const { animation } = config;
    const time = state.clock.getElapsedTime();

    switch (animation.type) {
      case 'rotate':
        if (animation.axis === 'x') {
          meshRef.current.rotation.x = time * animation.speed;
        } else if (animation.axis === 'y') {
          meshRef.current.rotation.y = time * animation.speed;
        } else if (animation.axis === 'z') {
          meshRef.current.rotation.z = time * animation.speed;
        }
        break;

      case 'float':
        meshRef.current.position.y =
          config.position[1] +
          Math.sin(time * animation.speed) * (animation.amplitude || 0.5);
        break;

      case 'scale':
        const scale =
          1 + Math.sin(time * animation.speed) * (animation.amplitude || 0.1);
        meshRef.current.scale.setScalar(scale);
        break;

      case 'orbit':
        const radius = animation.amplitude || 3;
        const offset = animation.offset || 0;
        meshRef.current.position.x =
          Math.cos(time * animation.speed + offset) * radius;
        meshRef.current.position.z =
          Math.sin(time * animation.speed + offset) * radius;
        break;

      case 'pulse':
        const pulseScale =
          1 + Math.sin(time * animation.speed) * (animation.amplitude || 0.2);
        meshRef.current.scale.setScalar(pulseScale);
        // Type-safe emissive intensity update
        const material = meshRef.current.material;
        if (material && 'emissiveIntensity' in material) {
          (material as THREE.MeshStandardMaterial).emissiveIntensity =
            0.1 + Math.sin(time * animation.speed * 2) * 0.05;
        }
        break;

      case 'wave':
        meshRef.current.position.y =
          config.position[1] +
          Math.sin(time * animation.speed + (animation.offset || 0)) *
            (animation.amplitude || 0.3);
        meshRef.current.rotation.z = Math.sin(time * animation.speed) * 0.1;
        break;
    }
  });

  const geometry = useMemo(() => {
    switch (config.type) {
      case 'box':
        return <boxGeometry args={[1, 1, 1]} />;
      case 'sphere':
        return <sphereGeometry args={[0.5, 32, 32]} />;
      case 'plane':
        return <planeGeometry args={[2, 2]} />;
      case 'cylinder':
        return <cylinderGeometry args={[0.5, 0.5, 1, 32]} />;
      case 'torus':
        return <torusGeometry args={[1, 0.3, 16, 100]} />;
      default:
        return <boxGeometry args={[1, 1, 1]} />;
    }
  }, [config.type]);

  return (
    <mesh
      ref={meshRef}
      position={config.position}
      rotation={config.rotation}
      scale={config.scale}
      castShadow={config.castShadow}
      receiveShadow={config.receiveShadow}
    >
      {geometry}
      <meshStandardMaterial
        color={config.material.color}
        roughness={config.material.roughness || 0.5}
        metalness={config.material.metalness || 0.1}
        transparent={config.material.transparent}
        opacity={config.material.opacity || 1}
        emissive={config.material.emissive || '#000000'}
        emissiveIntensity={config.material.emissiveIntensity || 0}
      />
    </mesh>
  );
};

// Particle system component
const ParticleSystem = ({
  config,
}: {
  config: NonNullable<AnimationConfig['particles']>[0];
}) => {
  const pointsRef = useRef<THREE.Points>(null);
  const positionsRef = useRef<Float32Array | null>(null);

  const { positions, colors } = useMemo(() => {
    const positions = new Float32Array(config.count * 3);
    const colors = new Float32Array(config.count * 3);
    const color = new THREE.Color(config.color);

    for (let i = 0; i < config.count; i++) {
      const i3 = i * 3;

      // Random positions within spread
      positions[i3] = (Math.random() - 0.5) * config.spread[0];
      positions[i3 + 1] = (Math.random() - 0.5) * config.spread[1];
      positions[i3 + 2] = (Math.random() - 0.5) * config.spread[2];

      // Set colors
      colors[i3] = color.r;
      colors[i3 + 1] = color.g;
      colors[i3 + 2] = color.b;
    }

    positionsRef.current = positions;
    return { positions, colors };
  }, [config]);

  useFrame(() => {
    if (!pointsRef.current || !positionsRef.current) return;

    const positions = positionsRef.current;

    for (let i = 0; i < config.count; i++) {
      const i3 = i * 3;

      switch (config.animation.type) {
        case 'flow':
          positions[i3 + 1] += config.animation.speed * 0.01;
          if (positions[i3 + 1] > config.spread[1] / 2) {
            positions[i3 + 1] = -config.spread[1] / 2;
          }
          break;

        case 'spiral':
          const radius = Math.sqrt(positions[i3] ** 2 + positions[i3 + 2] ** 2);
          const angle =
            Math.atan2(positions[i3 + 2], positions[i3]) +
            config.animation.speed * 0.01;
          positions[i3] = Math.cos(angle) * radius;
          positions[i3 + 2] = Math.sin(angle) * radius;
          break;

        case 'random':
          positions[i3] +=
            (Math.random() - 0.5) * config.animation.speed * 0.001;
          positions[i3 + 1] +=
            (Math.random() - 0.5) * config.animation.speed * 0.001;
          positions[i3 + 2] +=
            (Math.random() - 0.5) * config.animation.speed * 0.001;
          break;
      }
    }

    pointsRef.current.geometry.attributes.position.needsUpdate = true;
  });

  return (
    <points ref={pointsRef}>
      <bufferGeometry>
        <bufferAttribute attach="attributes-position" args={[positions, 3]} />
        <bufferAttribute attach="attributes-color" args={[colors, 3]} />
      </bufferGeometry>
      <pointsMaterial
        size={config.size}
        transparent={config.material.transparent}
        opacity={config.material.opacity}
        vertexColors
        blending={config.material.blending || THREE.AdditiveBlending}
      />
    </points>
  );
};

// Main 3D scene component
const Scene = ({
  config,
  enableControls = false,
  autoRotate = false,
}: {
  config: AnimationConfig;
  enableControls?: boolean;
  autoRotate?: boolean;
}) => {
  return (
    <>
      {/* Camera */}
      <PerspectiveCamera
        makeDefault
        position={config.camera.position}
        fov={config.camera.fov || 75}
        near={config.camera.near || 0.1}
        far={config.camera.far || 1000}
      />

      {/* Lighting */}
      {config.lights.map((light, index) => {
        switch (light.type) {
          case 'ambient':
            return (
              <ambientLight
                key={index}
                color={light.color}
                intensity={light.intensity}
              />
            );
          case 'directional':
            return (
              <directionalLight
                key={index}
                color={light.color}
                intensity={light.intensity}
                position={light.position || [5, 5, 5]}
                castShadow={light.castShadow}
                shadow-mapSize-width={2048}
                shadow-mapSize-height={2048}
              />
            );
          case 'point':
            return (
              <pointLight
                key={index}
                color={light.color}
                intensity={light.intensity}
                position={light.position || [0, 0, 0]}
              />
            );
          case 'spot':
            return (
              <spotLight
                key={index}
                color={light.color}
                intensity={light.intensity}
                position={light.position || [0, 5, 0]}
                target-position={light.target || [0, 0, 0]}
                angle={Math.PI / 6}
                penumbra={0.5}
              />
            );
          default:
            return null;
        }
      })}

      {/* 3D Objects */}
      {config.objects.map((obj, index) => (
        <AnimatedObject key={index} config={obj} index={index} />
      ))}

      {/* Particle Systems */}
      {config.particles?.map((particles, index) => (
        <ParticleSystem key={index} config={particles} />
      ))}

      {/* Controls */}
      {enableControls && (
        <OrbitControls
          enableZoom={true}
          enablePan={false}
          autoRotate={autoRotate}
          autoRotateSpeed={0.5}
        />
      )}

      {/* Environment */}
      <Environment preset="sunset" />

      {/* Fog */}
      {config.scene.fog && (
        <fog
          attach="fog"
          args={[
            config.scene.fog.color,
            config.scene.fog.near,
            config.scene.fog.far,
          ]}
        />
      )}
    </>
  );
};

// Main ThreeJsHero component
const ThreeJsHero = ({
  title,
  description,
  buttonText,
  buttonHref,
  animationConfig,
  className = '',
  enableControls = false,
  autoRotate = false,
  showStats = false,
}: ThreeJsHeroProps) => {
  const { theme } = useTheme();

  // Adjust scene background based on theme
  const sceneBackground = useMemo(() => {
    if (animationConfig.scene.background) {
      return animationConfig.scene.background;
    }
    return theme === 'dark'
      ? 'hsl(var(--background))'
      : 'hsl(var(--background))';
  }, [animationConfig.scene.background, theme]);

  return (
    <section
      className={`relative h-screen w-full overflow-hidden ${className}`}
    >
      {/* 3D Canvas Background */}
      <div className="absolute inset-0 z-0">
        <Canvas
          shadows={animationConfig.performance?.shadowMapEnabled}
          dpr={animationConfig.performance?.pixelRatio || [1, 2]}
          gl={{
            antialias: animationConfig.performance?.antialias,
            powerPreference:
              animationConfig.performance?.powerPreference ||
              'high-performance',
          }}
          style={{
            background: sceneBackground,
          }}
        >
          <Suspense fallback={<Loader />}>
            <Scene
              config={animationConfig}
              enableControls={enableControls}
              autoRotate={autoRotate}
            />
          </Suspense>

          {/* Performance Stats (development only) */}
          {showStats && process.env.NODE_ENV === 'development' && <Stats />}
        </Canvas>
      </div>

      {/* Hero Content Overlay */}
      <div className="relative z-10 flex h-full items-center justify-center">
        <div className="mx-auto max-w-4xl px-4 text-center">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, ease: 'easeOut' }}
          >
            <h1
              className="text-foreground mb-6 text-4xl font-bold drop-shadow-lg md:text-6xl"
              dangerouslySetInnerHTML={{ __html: title }}
            />
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.2, ease: 'easeOut' }}
          >
            <p className="text-muted-foreground mx-auto mb-8 max-w-2xl text-lg drop-shadow-md md:text-xl">
              {description}
            </p>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.4, ease: 'easeOut' }}
          >
            <Button
              size="lg"
              className="bg-primary hover:bg-primary/90 text-primary-foreground transform-gpu px-8 py-3 text-lg shadow-lg transition-all duration-200 hover:scale-105 hover:shadow-xl"
              asChild
            >
              <a href={buttonHref}>{buttonText}</a>
            </Button>
          </motion.div>
        </div>
      </div>

      {/* Gradient Overlay for Better Text Readability */}
      <div className="from-background/20 to-background/40 pointer-events-none absolute inset-0 z-5 bg-gradient-to-b via-transparent" />

      {/* Optional Controls Info */}
      {enableControls && (
        <div className="text-muted-foreground bg-background/80 absolute bottom-4 left-4 z-20 rounded-lg px-3 py-2 text-xs backdrop-blur-sm">
          <p>Click and drag to rotate • Scroll to zoom</p>
        </div>
      )}
    </section>
  );
};

export default ThreeJsHero;
