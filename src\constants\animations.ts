import type { AnimationConfig } from '@/types';

/**
 * Centris Document Processing Animation Configuration
 *
 * This configuration creates a realistic representation of document processing:
 * - Document scanning with moving scanner head
 * - Text and data extraction visualization
 * - Digital transformation of physical documents
 * - Real-time processing workflow
 *
 * Design Philosophy:
 * - Represents actual document scanning and processing
 * - Shows text/data extraction with floating characters and numbers
 * - Visualizes digital transformation workflow
 * - Professional scanner-like environment
 * - Performance-optimized for web deployment
 */
export const defaultAnimationConfig: AnimationConfig = {
  scene: {
    background: 'hsl(var(--background))',
    fog: {
      color: 'hsl(var(--muted))',
      near: 15,
      far: 45,
    },
  },

  camera: {
    position: [0, 4, 12],
    fov: 70,
    near: 0.1,
    far: 100,
  },

  lights: [
    // Ambient lighting for document scanning environment
    {
      type: 'ambient',
      color: 'hsl(var(--foreground))',
      intensity: 0.5,
    },
    // Scanner light - bright directional light simulating document scanner
    {
      type: 'directional',
      color: '#ffffff',
      intensity: 1.8,
      position: [0, 8, 3],
      castShadow: true,
    },
    // Processing light with Centris blue tint
    {
      type: 'point',
      color: 'hsl(var(--primary))',
      intensity: 1.0,
      position: [4, 6, 2],
    },
    // Data extraction highlight
    {
      type: 'spot',
      color: 'hsl(var(--accent))',
      intensity: 1.2,
      position: [0, 10, 6],
      target: [0, 0, 0],
    },
  ],

  objects: [
    // Document Scanner Platform - the main scanning surface
    {
      type: 'plane',
      position: [0, 0, 0],
      scale: [5, 3.5, 1],
      rotation: [-Math.PI / 2, 0, 0],
      material: {
        color: '#f8f9fa',
        metalness: 0.1,
        roughness: 0.8,
        emissive: 'hsl(var(--primary))',
        emissiveIntensity: 0.02,
      },
      animation: {
        type: 'pulse',
        speed: 1.2,
        amplitude: 0.05,
      },
      receiveShadow: true,
    },

    // Document Pages - physical documents being scanned
    {
      type: 'plane',
      position: [-1.2, 0.1, 0.5],
      scale: [1.5, 2, 0.02],
      rotation: [0, 0, 0.05],
      material: {
        color: '#ffffff',
        metalness: 0.0,
        roughness: 0.9,
        transparent: true,
        opacity: 0.95,
      },
      animation: {
        type: 'float',
        speed: 0.6,
        amplitude: 0.1,
      },
      castShadow: true,
    },

    {
      type: 'plane',
      position: [1.2, 0.1, -0.3],
      scale: [1.5, 2, 0.02],
      rotation: [0, 0, -0.08],
      material: {
        color: '#fefefe',
        metalness: 0.0,
        roughness: 0.9,
        transparent: true,
        opacity: 0.9,
      },
      animation: {
        type: 'float',
        speed: 0.8,
        amplitude: 0.15,
        offset: Math.PI / 2,
      },
      castShadow: true,
    },

    // Scanner Head - moves across documents
    {
      type: 'box',
      position: [0, 2.5, 0],
      scale: [6, 0.4, 1.2],
      material: {
        color: 'hsl(var(--secondary))',
        metalness: 0.7,
        roughness: 0.3,
        emissive: 'hsl(var(--secondary))',
        emissiveIntensity: 0.1,
      },
      animation: {
        type: 'wave',
        speed: 0.8,
        amplitude: 0.6,
        axis: 'x',
      },
      castShadow: true,
    },

    // Text Extraction Nodes - represent extracted characters
    {
      type: 'sphere',
      position: [2, 1.5, 1],
      scale: [0.15, 0.15, 0.15],
      material: {
        color: 'hsl(var(--accent))',
        metalness: 0.8,
        roughness: 0.2,
        emissive: 'hsl(var(--accent))',
        emissiveIntensity: 0.4,
      },
      animation: {
        type: 'orbit',
        speed: 1.8,
        amplitude: 1.2,
      },
    },

    {
      type: 'sphere',
      position: [-2, 1.8, 0.8],
      scale: [0.12, 0.12, 0.12],
      material: {
        color: 'hsl(var(--chart-1))',
        metalness: 0.8,
        roughness: 0.2,
        emissive: 'hsl(var(--chart-1))',
        emissiveIntensity: 0.4,
      },
      animation: {
        type: 'orbit',
        speed: -1.5,
        amplitude: 1.0,
        offset: Math.PI / 3,
      },
    },

    {
      type: 'sphere',
      position: [0, 2, 1.5],
      scale: [0.1, 0.1, 0.1],
      material: {
        color: 'hsl(var(--chart-2))',
        metalness: 0.8,
        roughness: 0.2,
        emissive: 'hsl(var(--chart-2))',
        emissiveIntensity: 0.4,
      },
      animation: {
        type: 'orbit',
        speed: 2.2,
        amplitude: 0.8,
        offset: Math.PI / 6,
      },
    },

    // Digital Processing Core - central data processor
    {
      type: 'cylinder',
      position: [0, 4, -2],
      scale: [1, 1.8, 1],
      material: {
        color: 'hsl(var(--primary))',
        metalness: 0.6,
        roughness: 0.4,
        emissive: 'hsl(var(--primary))',
        emissiveIntensity: 0.15,
      },
      animation: {
        type: 'rotate',
        speed: 1.2,
        axis: 'y',
      },
      castShadow: true,
    },

    // Data Flow Ring - represents processed information
    {
      type: 'torus',
      position: [0, 5.5, -1.5],
      scale: [1.8, 1.8, 1.8],
      rotation: [Math.PI / 2, 0, 0],
      material: {
        color: 'hsl(var(--chart-3))',
        metalness: 0.5,
        roughness: 0.5,
        transparent: true,
        opacity: 0.8,
      },
      animation: {
        type: 'rotate',
        speed: 2.0,
        axis: 'z',
      },
    },

    // Background Grid - scanning environment
    {
      type: 'plane',
      position: [0, -0.5, -6],
      scale: [20, 20, 1],
      rotation: [-Math.PI / 2, 0, 0],
      material: {
        color: 'hsl(var(--muted))',
        metalness: 0.1,
        roughness: 0.9,
        transparent: true,
        opacity: 0.15,
      },
      receiveShadow: true,
    },
  ],

  particles: [
    // Text Extraction Particles - represent letters and words being extracted
    {
      count: 150,
      position: [0, 0.5, 0],
      spread: [4, 2, 3],
      size: 0.08,
      color: 'hsl(var(--primary))',
      animation: {
        type: 'flow',
        speed: 0.8,
        direction: [0, 1, 0.3],
      },
      material: {
        transparent: true,
        opacity: 0.7,
      },
    },

    // Number Extraction Particles - represent numerical data being processed
    {
      count: 80,
      position: [0, 0.5, 0],
      spread: [3, 1.5, 2],
      size: 0.06,
      color: 'hsl(var(--accent))',
      animation: {
        type: 'spiral',
        speed: 1.2,
      },
      material: {
        transparent: true,
        opacity: 0.8,
      },
    },

    // Digital Transformation Particles - represent data being digitized
    {
      count: 120,
      position: [0, 3, -1],
      spread: [2, 3, 2],
      size: 0.04,
      color: 'hsl(var(--chart-1))',
      animation: {
        type: 'flow',
        speed: 1.5,
        direction: [0, 1, -0.5],
      },
      material: {
        transparent: true,
        opacity: 0.6,
      },
    },

    // Scanner Light Particles - represent scanning beam
    {
      count: 60,
      position: [0, 2.5, 0],
      spread: [5, 0.2, 1],
      size: 0.03,
      color: '#ffffff',
      animation: {
        type: 'flow',
        speed: 2.0,
        direction: [1, 0, 0],
      },
      material: {
        transparent: true,
        opacity: 0.9,
      },
    },
  ],

  postProcessing: {
    bloom: {
      enabled: true,
      strength: 0.3,
      radius: 0.8,
      threshold: 0.1,
    },
  },

  physics: {
    enabled: false, // Disabled for performance, can be enabled for interactive demos
    gravity: [0, -9.81, 0],
    objects: [],
  },

  performance: {
    antialias: true,
    shadowMapEnabled: true,
    pixelRatio:
      typeof window !== 'undefined' ? Math.min(window.devicePixelRatio, 2) : 1,
    powerPreference: 'high-performance',
  },
};

/**
 * Alternative configuration for About page - more subtle and professional
 */
export const aboutAnimationConfig: AnimationConfig = {
  scene: {
    background: 'hsl(var(--background))',
    fog: {
      color: 'hsl(var(--muted))',
      near: 15,
      far: 40,
    },
  },

  camera: {
    position: [0, 1, 6],
    fov: 60,
  },

  lights: [
    {
      type: 'ambient',
      color: 'hsl(var(--foreground))',
      intensity: 0.6,
    },
    {
      type: 'directional',
      color: 'hsl(var(--primary))',
      intensity: 0.8,
      position: [3, 5, 3],
    },
  ],

  objects: [
    {
      type: 'sphere',
      position: [0, 0, 0],
      scale: [1, 1, 1],
      material: {
        color: 'hsl(var(--primary))',
        metalness: 0.5,
        roughness: 0.3,
      },
      animation: {
        type: 'rotate',
        speed: 0.2,
        axis: 'y',
      },
    },
  ],

  performance: {
    antialias: true,
    shadowMapEnabled: false,
    pixelRatio: 1,
  },
};
