import { AnimationConfig } from '@/types';

/**
 * Advanced Three.js Animation Configuration for Centris System Hero Component
 *
 * This configuration reflects Centris's identity as a leading-edge electronic document
 * processing system. The animations metaphorically represent data flow, document processing,
 * and the digital transformation that Centris brings to businesses.
 *
 * Design Philosophy:
 * - Blue gradient colors from Centris logo (primary, secondary, accent)
 * - Professional yet innovative aesthetic
 * - Data flow and document processing metaphors
 * - Modern tech-forward visual language
 * - Performance-optimized for web deployment
 */
export const defaultAnimationConfig: AnimationConfig = {
  scene: {
    // Subtle gradient background that complements the Centris color palette
    background:
      'linear-gradient(135deg, hsl(var(--background)) 0%, hsl(var(--muted)) 50%, hsl(var(--background)) 100%)',
    fog: {
      color: 'hsl(var(--muted))',
      near: 10,
      far: 50,
    },
  },

  camera: {
    position: [0, 2, 8],
    fov: 75,
    near: 0.1,
    far: 100,
  },

  lights: [
    // Ambient lighting for overall scene illumination
    {
      type: 'ambient',
      color: 'hsl(var(--foreground))',
      intensity: 0.4,
    },
    // Primary directional light with Centris blue tint
    {
      type: 'directional',
      color: 'hsl(var(--primary))',
      intensity: 1.2,
      position: [5, 8, 5],
      castShadow: true,
    },
    // Secondary accent light for depth
    {
      type: 'point',
      color: 'hsl(var(--secondary))',
      intensity: 0.8,
      position: [-3, 4, 2],
    },
    // Accent cyan highlight light
    {
      type: 'spot',
      color: 'hsl(var(--accent))',
      intensity: 1.0,
      position: [0, 6, 4],
      target: [0, 0, 0],
    },
  ],

  objects: [
    // Central Data Core - represents the heart of Centris system
    {
      type: 'sphere',
      position: [0, 0, 0],
      scale: [1.5, 1.5, 1.5],
      material: {
        color: 'hsl(var(--primary))',
        metalness: 0.8,
        roughness: 0.2,
        emissive: 'hsl(var(--primary))',
        emissiveIntensity: 0.1,
      },
      animation: {
        type: 'rotate',
        speed: 0.5,
        axis: 'y',
      },
      castShadow: true,
    },

    // Document Processing Rings - represent workflow stages
    {
      type: 'torus',
      position: [0, 0, 0],
      scale: [2.5, 2.5, 2.5],
      rotation: [Math.PI / 4, 0, 0],
      material: {
        color: 'hsl(var(--secondary))',
        metalness: 0.6,
        roughness: 0.3,
        transparent: true,
        opacity: 0.7,
      },
      animation: {
        type: 'rotate',
        speed: -0.3,
        axis: 'x',
      },
    },

    // Data Flow Cubes - represent document types
    {
      type: 'box',
      position: [3, 1, 0],
      scale: [0.5, 0.5, 0.5],
      material: {
        color: 'hsl(var(--accent))',
        metalness: 0.7,
        roughness: 0.2,
      },
      animation: {
        type: 'orbit',
        speed: 1.0,
        amplitude: 3,
      },
      castShadow: true,
    },

    {
      type: 'box',
      position: [-3, -1, 0],
      scale: [0.4, 0.4, 0.4],
      material: {
        color: 'hsl(var(--chart-1))',
        metalness: 0.5,
        roughness: 0.4,
      },
      animation: {
        type: 'orbit',
        speed: -0.8,
        amplitude: 2.5,
        offset: Math.PI,
      },
      castShadow: true,
    },

    // Processing Nodes - smaller elements representing system modules
    {
      type: 'cylinder',
      position: [0, 2.5, -2],
      scale: [0.3, 1, 0.3],
      material: {
        color: 'hsl(var(--chart-2))',
        metalness: 0.9,
        roughness: 0.1,
        emissive: 'hsl(var(--chart-2))',
        emissiveIntensity: 0.05,
      },
      animation: {
        type: 'pulse',
        speed: 2.0,
        amplitude: 0.2,
      },
    },

    {
      type: 'cylinder',
      position: [2, -2, 1],
      scale: [0.25, 0.8, 0.25],
      material: {
        color: 'hsl(var(--chart-3))',
        metalness: 0.8,
        roughness: 0.15,
      },
      animation: {
        type: 'wave',
        speed: 1.5,
        amplitude: 0.3,
        offset: Math.PI / 2,
      },
    },

    // Background Elements - subtle geometric shapes for depth
    {
      type: 'plane',
      position: [0, -3, -5],
      scale: [15, 15, 1],
      rotation: [-Math.PI / 2, 0, 0],
      material: {
        color: 'hsl(var(--muted))',
        metalness: 0.1,
        roughness: 0.9,
        transparent: true,
        opacity: 0.3,
      },
      receiveShadow: true,
    },
  ],

  particles: [
    // Data Stream Particles - represent information flow
    {
      count: 200,
      position: [0, 0, 0],
      spread: [8, 6, 8],
      size: 0.05,
      color: 'hsl(var(--primary))',
      animation: {
        type: 'flow',
        speed: 0.5,
        direction: [0, 1, 0],
      },
      material: {
        transparent: true,
        opacity: 0.6,
      },
    },

    // Processing Particles - represent active document processing
    {
      count: 100,
      position: [0, 0, 0],
      spread: [4, 4, 4],
      size: 0.03,
      color: 'hsl(var(--accent))',
      animation: {
        type: 'spiral',
        speed: 1.0,
      },
      material: {
        transparent: true,
        opacity: 0.8,
      },
    },
  ],

  postProcessing: {
    bloom: {
      enabled: true,
      strength: 0.3,
      radius: 0.8,
      threshold: 0.1,
    },
  },

  physics: {
    enabled: false, // Disabled for performance, can be enabled for interactive demos
    gravity: [0, -9.81, 0],
    objects: [],
  },

  performance: {
    antialias: true,
    shadowMapEnabled: true,
    pixelRatio: typeof window !== 'undefined' ? Math.min(window.devicePixelRatio, 2) : 1,
    powerPreference: 'high-performance',
  },
};

/**
 * Alternative configuration for About page - more subtle and professional
 */
export const aboutAnimationConfig: AnimationConfig = {
  scene: {
    background: 'hsl(var(--background))',
    fog: {
      color: 'hsl(var(--muted))',
      near: 15,
      far: 40,
    },
  },

  camera: {
    position: [0, 1, 6],
    fov: 60,
  },

  lights: [
    {
      type: 'ambient',
      color: 'hsl(var(--foreground))',
      intensity: 0.6,
    },
    {
      type: 'directional',
      color: 'hsl(var(--primary))',
      intensity: 0.8,
      position: [3, 5, 3],
    },
  ],

  objects: [
    {
      type: 'sphere',
      position: [0, 0, 0],
      scale: [1, 1, 1],
      material: {
        color: 'hsl(var(--primary))',
        metalness: 0.5,
        roughness: 0.3,
      },
      animation: {
        type: 'rotate',
        speed: 0.2,
        axis: 'y',
      },
    },
  ],

  performance: {
    antialias: true,
    shadowMapEnabled: false,
    pixelRatio: 1,
  },
};
