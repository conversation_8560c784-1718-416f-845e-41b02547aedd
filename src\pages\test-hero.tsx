'use client';

import React from 'react';
import ThreeJsHero from '@/components/three-js-hero';
import { defaultAnimationConfig } from '@/constants/animations';

/**
 * Test page for the new Centris document processing animation
 * This page allows us to preview the new animation that represents:
 * - Document scanning with moving scanner head
 * - Text and data extraction visualization  
 * - Digital transformation of physical documents
 * - Real-time processing workflow
 */
export default function TestHeroPage() {
  return (
    <div className="min-h-screen">
      <ThreeJsHero
        animationConfig={defaultAnimationConfig}
        enableControls={true}
        autoRotate={false}
        showStats={true}
        className="h-screen"
      />
      
      {/* Info panel */}
      <div className="fixed top-4 right-4 z-20 max-w-sm rounded-lg bg-card/90 p-4 backdrop-blur-sm">
        <h3 className="mb-2 font-semibold text-card-foreground">
          Centris Document Processing Animation
        </h3>
        <ul className="space-y-1 text-sm text-muted-foreground">
          <li>• Scanner platform with documents</li>
          <li>• Moving scanner head</li>
          <li>• Text extraction particles (blue)</li>
          <li>• Number extraction particles (cyan)</li>
          <li>• Digital transformation flow</li>
          <li>• Processing core visualization</li>
        </ul>
        <div className="mt-3 text-xs text-muted-foreground">
          <p>Drag to rotate • Scroll to zoom</p>
        </div>
      </div>
    </div>
  );
}
