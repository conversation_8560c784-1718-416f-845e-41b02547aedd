import * as THREE from 'three';

// Base animation configuration types
export interface AnimationConfig {
  scene: {
    background?: string;
    fog?: {
      color: string;
      near: number;
      far: number;
    };
  };
  camera: {
    position: [number, number, number];
    fov?: number;
    near?: number;
    far?: number;
  };
  lights: Array<{
    type: 'ambient' | 'directional' | 'point' | 'spot' | 'hemisphere';
    color: string;
    intensity: number;
    position?: [number, number, number];
    target?: [number, number, number];
    castShadow?: boolean;
  }>;
  objects: Array<{
    type:
      | 'box'
      | 'sphere'
      | 'plane'
      | 'cylinder'
      | 'torus'
      | 'gltf'
      | 'particles';
    position: [number, number, number];
    rotation?: [number, number, number];
    scale?: [number, number, number];
    material: {
      color: string;
      roughness?: number;
      metalness?: number;
      transparent?: boolean;
      opacity?: number;
      emissive?: string;
      emissiveIntensity?: number;
    };
    animation?: {
      type: 'rotate' | 'float' | 'scale' | 'orbit' | 'pulse' | 'wave';
      speed: number;
      axis?: 'x' | 'y' | 'z';
      amplitude?: number;
      offset?: number;
    };
    gltfPath?: string;
    castShadow?: boolean;
    receiveShadow?: boolean;
  }>;
  particles?: Array<{
    count: number;
    position: [number, number, number];
    spread: [number, number, number];
    size: number;
    color: string;
    animation: {
      type: 'flow' | 'spiral' | 'random' | 'wave';
      speed: number;
      direction?: [number, number, number];
    };
    material: {
      transparent: boolean;
      opacity: number;
      blending?: THREE.Blending;
    };
  }>;
  postProcessing?: {
    bloom?: {
      enabled: boolean;
      strength?: number;
      radius?: number;
      threshold?: number;
    };
    depthOfField?: {
      enabled: boolean;
      focusDistance?: number;
      focalLength?: number;
      bokehScale?: number;
    };
  };
  physics?: {
    enabled: boolean;
    gravity: [number, number, number];
    objects: Array<{
      objectIndex: number;
      type: 'static' | 'dynamic' | 'kinematic';
      mass?: number;
      restitution?: number;
      friction?: number;
    }>;
  };
  performance?: {
    antialias?: boolean;
    shadowMapEnabled?: boolean;
    shadowMapType?: THREE.ShadowMapType;
    pixelRatio?: number;
    powerPreference?: 'default' | 'high-performance' | 'low-power';
  };
}

// Shader material configuration
export interface ShaderConfig {
  uniforms: Record<string, { value: unknown }>;
  vertexShader: string;
  fragmentShader: string;
  transparent?: boolean;
  blending?: THREE.Blending;
}

// GLTF model configuration
export interface GLTFConfig {
  path: string;
  position: [number, number, number];
  rotation?: [number, number, number];
  scale?: [number, number, number];
  animations?: Array<{
    name: string;
    loop: boolean;
    timeScale?: number;
  }>;
}

// Particle system configuration
export interface ParticleSystemConfig {
  count: number;
  geometry: 'sphere' | 'box' | 'plane';
  material: {
    color: string;
    size: number;
    transparent: boolean;
    opacity: number;
    blending?: THREE.Blending;
  };
  behavior: {
    type: 'static' | 'flow' | 'orbit' | 'random';
    speed: number;
    range: [number, number, number];
  };
}

// Hero component props
export interface ThreeJsHeroProps {
  title: string;
  description: string;
  buttonText: string;
  buttonHref: string;
  animationConfig: AnimationConfig;
  className?: string;
  enableControls?: boolean;
  autoRotate?: boolean;
  showStats?: boolean;
}

// Animation state management
export interface AnimationState {
  isPlaying: boolean;
  currentTime: number;
  duration: number;
  loop: boolean;
}

// Performance monitoring
export interface PerformanceMetrics {
  fps: number;
  drawCalls: number;
  triangles: number;
  geometries: number;
  textures: number;
  memory: {
    geometries: number;
    textures: number;
  };
}
